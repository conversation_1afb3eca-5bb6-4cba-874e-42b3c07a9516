"""
Async service for managing shared queries.
"""
import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from models.shared_query import SharedQuery
from models.query import Query
from models.user_query import UserQuery
from utils.multi_schema_db_manager import MultiSchemaDBManager
from utils.logging import logger

class AsyncSharedQueryService:
    """Service for managing shared queries."""
    
    def __init__(self):
        self.db_manager = MultiSchemaDBManager()
    
    async def create_share(
        self,
        query_id: int,
        user_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None,
        expires_in_days: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Create a new share for a query.
        
        Args:
            query_id: ID of the query to share
            user_id: User ID (auth0_sub) of the query owner
            title: Optional custom title
            description: Optional description
            expires_in_days: Optional expiration in days
            
        Returns:
            Dict with success status and share data
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Verify user owns the query
                query_result = await session.execute(
                    select(Query)
                    .join(UserQuery)
                    .where(
                        and_(
                            Query.id == query_id,
                            UserQuery.uid == user_id
                        )
                    )
                )
                query = query_result.scalar_one_or_none()
                
                if not query:
                    return {
                        "success": False,
                        "error": "Query not found or access denied"
                    }
                
                # Check if already shared
                existing_share = await session.execute(
                    select(SharedQuery).where(
                        and_(
                            SharedQuery.query_id == query_id,
                            SharedQuery.user_id == user_id,
                            SharedQuery.is_active == True
                        )
                    )
                )
                existing = existing_share.scalar_one_or_none()
                
                if existing:
                    return {
                        "success": True,
                        "share_id": existing.share_id,
                        "share_url": f"https://addxgo.io/ai/{existing.share_id}",
                        "existing": True
                    }
                
                # Calculate expiration
                expires_at = None
                if expires_in_days:
                    expires_at = datetime.datetime.now() + datetime.timedelta(days=expires_in_days)
                
                # Create new share
                shared_query = SharedQuery(
                    share_id=SharedQuery.generate_share_id(),
                    query_id=query_id,
                    user_id=user_id,
                    shared_by=user_id,
                    title=title,
                    description=description,
                    expires_at=expires_at
                )
                
                session.add(shared_query)
                await session.commit()
                await session.refresh(shared_query)
                
                return {
                    "success": True,
                    "share_id": shared_query.share_id,
                    "share_url": f"https://addxgo.io/ai/{shared_query.share_id}",
                    "expires_at": shared_query.expires_at.isoformat() if shared_query.expires_at else None,
                    "existing": False
                }
                
        except Exception as e:
            logger.error(f"Error creating share: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_shared_query(self, share_id: str) -> Dict[str, Any]:
        """
        Get a shared query by share_id for public access.
        
        Args:
            share_id: The UUID share identifier
            
        Returns:
            Dict with query data or error
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Get shared query with related query data
                result = await session.execute(
                    select(SharedQuery, Query)
                    .join(Query, SharedQuery.query_id == Query.id)
                    .where(SharedQuery.share_id == share_id)
                )
                row = result.first()
                
                if not row:
                    return {
                        "success": False,
                        "error": "Shared query not found"
                    }
                
                shared_query, query = row
                
                # Check if accessible
                if not shared_query.is_accessible():
                    return {
                        "success": False,
                        "error": "Shared query is no longer accessible"
                    }
                
                # Increment view count
                shared_query.increment_view_count()
                await session.commit()
                
                # Return sanitized data for public consumption
                return {
                    "success": True,
                    "data": {
                        "share_info": shared_query.to_public_dict(),
                        "query": {
                            "user_query": query.user_query,
                            "llm_answer": query.llm_answer,
                            "model": query.model,
                            "created_at": query.created_at.isoformat() if query.created_at else None,
                            # Filter resources to remove sensitive data
                            "resources": self._sanitize_resources(query.resources) if query.resources else []
                        }
                    }
                }
                
        except Exception as e:
            logger.error(f"Error getting shared query: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_user_shares(self, user_id: str, page: int = 1, page_size: int = 10) -> Dict[str, Any]:
        """Get all shares created by a user."""
        try:
            async with self.db_manager.get_async_session() as session:
                offset = (page - 1) * page_size
                
                # Get total count
                count_result = await session.execute(
                    select(func.count(SharedQuery.id))
                    .where(SharedQuery.user_id == user_id)
                )
                total_count = count_result.scalar()
                
                # Get shares with query data
                result = await session.execute(
                    select(SharedQuery, Query)
                    .join(Query, SharedQuery.query_id == Query.id)
                    .where(SharedQuery.user_id == user_id)
                    .order_by(SharedQuery.created_at.desc())
                    .offset(offset)
                    .limit(page_size)
                )
                
                shares = []
                for shared_query, query in result:
                    share_data = shared_query.to_dict()
                    share_data["query_preview"] = query.user_query[:100] + "..." if len(query.user_query) > 100 else query.user_query
                    share_data["share_url"] = f"https://addxgo.io/ai/{shared_query.share_id}"
                    shares.append(share_data)
                
                return {
                    "success": True,
                    "shares": shares,
                    "pagination": {
                        "total_count": total_count,
                        "page": page,
                        "page_size": page_size,
                        "total_pages": (total_count + page_size - 1) // page_size
                    }
                }
                
        except Exception as e:
            logger.error(f"Error getting user shares: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def revoke_share(self, share_id: str, user_id: str) -> Dict[str, Any]:
        """Revoke/deactivate a share."""
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(SharedQuery).where(
                        and_(
                            SharedQuery.share_id == share_id,
                            SharedQuery.user_id == user_id
                        )
                    )
                )
                shared_query = result.scalar_one_or_none()
                
                if not shared_query:
                    return {
                        "success": False,
                        "error": "Share not found or access denied"
                    }
                
                shared_query.is_active = False
                await session.commit()
                
                return {
                    "success": True,
                    "message": "Share revoked successfully"
                }
                
        except Exception as e:
            logger.error(f"Error revoking share: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _sanitize_resources(self, resources: List[Dict]) -> List[Dict]:
        """Remove sensitive information from resources for public sharing."""
        sanitized = []
        for resource in resources:
            # Only include safe resource types and remove sensitive data
            if resource.get("function_name") in ["web_search", "get_stock_price", "get_company_info"]:
                sanitized_resource = {
                    "function_name": resource.get("function_name"),
                    "timestamp": resource.get("timestamp"),
                    # Include only non-sensitive parts of output
                    "summary": "External data source used"
                }
                sanitized.append(sanitized_resource)
        return sanitized
